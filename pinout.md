# ESP32 Soil Moisture System - UPDATED SAFE PIN ASSIGNMENTS

## Current Safe Pin Configuration:
- **Soil Moisture Sensor** = GPIO32 (Safe ADC pin)
- **DHT22 Temperature/Humidity** = GPIO21 (Safe digital I/O pin)
- **Relay/LED Control** = GPIO18 (Safe digital output pin)

## Previous Problematic Pins (AVOID):
- ~~Soil Moisture Sensor = GPIO2~~ ❌ Boot strap pin
- ~~DHT22 = GPIO15~~ ❌ Boot strap pin
- ~~Relay/LED = GPIO0~~ ❌ Boot strap pin (must be HIGH to boot)

## Why These Pins Are Safe:
- **GPIO32**: ADC4 channel, no boot restrictions, perfect for analog sensors
- **GPIO21**: Default I2C SDA but can be used as digital I/O, no boot restrictions
- **GPIO18**: Default VSPI SCK but can be used as digital output, no boot restrictions

## Pins to Always Avoid:
- **Boot Strap Pins**: GPIO0, GPIO2, GPIO4, GPIO5, GPIO12, GPIO15 (affect boot mode)
- **Reserved Flash Pins**: GPIO6-GPIO11 (connected to onboard flash memory)
- **Input-Only Pins**: GPIO34-GPIO39 (cannot drive outputs like relays)
