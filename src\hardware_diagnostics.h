/*
 * ESP32 Hardware Diagnostics Module
 *
 * Comprehensive hardware testing and validation for ESP32 soil moisture system
 * Provides individual component testing, pin validation, and error isolation
 *
 * Author: ESP32 Soil Moisture Project
 * Date: 2025
 */

#ifndef HARDWARE_DIAGNOSTICS_H
#define HARDWARE_DIAGNOSTICS_H

#include <Arduino.h>
#include <DHT.h>

// ==================== DIAGNOSTIC RESULT STRUCTURES ====================
struct PinTestResult {
  int pin;
  String name;
  bool canRead;
  bool canWrite;
  bool hasInternalPullup;
  bool isBootStrapPin;
  bool isReservedPin;
  String warnings;
};

struct ComponentTestResult {
  String componentName;
  bool isWorking;
  bool isConnected;
  float testValue;
  String errorMessage;
  String recommendations;
};

struct SystemDiagnostics {
  String boardType;
  String chipModel;
  int chipRevision;
  int flashSize;
  int freeHeap;
  bool powerStable;
  ComponentTestResult moistureSensor;
  ComponentTestResult dhtSensor;
  ComponentTestResult relayModule;
  PinTestResult moisturePin;
  PinTestResult dhtPin;
  PinTestResult relayPin;
};

// ==================== HARDWARE DIAGNOSTICS CLASS ====================
class HardwareDiagnostics {
private:
  // Pin definitions (will be updated based on diagnostics)
  int moisturePin;
  int dhtPin;
  int relayPin;

  DHT* dht;
  bool initialized;

  // Internal diagnostic functions
  PinTestResult testPin(int pin, String name);
  bool isBootStrapPin(int pin);
  bool isReservedPin(int pin);
  ComponentTestResult testMoistureSensor(int pin);
  ComponentTestResult testDHTSensor(int pin);
  ComponentTestResult testRelayModule(int pin);
  void printSeparator(String title = "");
  void printComponentResult(ComponentTestResult result);

public:
  HardwareDiagnostics();
  ~HardwareDiagnostics();

  // Main diagnostic functions
  void initialize();
  SystemDiagnostics runFullDiagnostics();
  void printDiagnosticReport(SystemDiagnostics diagnostics);

  // Individual component tests
  bool testIndividualComponent(String component);
  void runInteractiveTest();

  // Pin validation and recommendation
  void validateCurrentPinAssignments();
  void recommendSafePins();

  // Board identification
  String identifyBoard();
  void printBoardInfo();

  // Utility functions
  bool isPinSafe(int pin);
  String getPinCapabilities(int pin);
  void scanAllPins();
};

// ==================== SAFE PIN RECOMMENDATIONS ====================
// Based on NodeMCU-32S pinout analysis
namespace SafePins {
  // Recommended safe pins for different functions
  const int MOISTURE_SENSOR_PINS[] = {32, 33, 34, 35, 36, 39}; // ADC pins
  const int DHT_SENSOR_PINS[] = {21, 22, 16, 17, 18, 19, 23, 25, 26, 27}; // Digital I/O
  const int RELAY_PINS[] = {18, 19, 21, 22, 23, 25, 26, 27}; // Digital output capable

  // Pins to avoid
  const int BOOT_STRAP_PINS[] = {0, 2, 4, 5, 12, 15};
  const int RESERVED_PINS[] = {6, 7, 8, 9, 10, 11}; // Flash pins
  const int INPUT_ONLY_PINS[] = {34, 35, 36, 39}; // Cannot drive outputs
}

#endif // HARDWARE_DIAGNOSTICS_H