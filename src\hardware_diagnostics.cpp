/*
 * ESP32 Hardware Diagnostics Module Implementation
 *
 * Comprehensive hardware testing and validation for ESP32 soil moisture system
 *
 * Author: ESP32 Soil Moisture Project
 * Date: 2025
 */

#include "hardware_diagnostics.h"
#include <esp_chip_info.h>
#include <esp_flash.h>

// ==================== CONSTRUCTOR/DESTRUCTOR ====================
HardwareDiagnostics::HardwareDiagnostics() {
  moisturePin = 2;  // Current default
  dhtPin = 15;      // Current default
  relayPin = 0;     // Current default
  dht = nullptr;
  initialized = false;
}

HardwareDiagnostics::~HardwareDiagnostics() {
  if (dht != nullptr) {
    delete dht;
  }
}

// ==================== INITIALIZATION ====================
void HardwareDiagnostics::initialize() {
  Serial.println("🔧 Initializing Hardware Diagnostics...");
  initialized = true;
  Serial.println("✓ Hardware Diagnostics ready");
}

// ==================== BOARD IDENTIFICATION ====================
String HardwareDiagnostics::identifyBoard() {
  esp_chip_info_t chip_info;
  esp_chip_info(&chip_info);

  String boardInfo = "ESP32";

  switch(chip_info.model) {
    case CHIP_ESP32:
      boardInfo = "ESP32 (Original)";
      break;
    case CHIP_ESP32S2:
      boardInfo = "ESP32-S2";
      break;
    case CHIP_ESP32S3:
      boardInfo = "ESP32-S3";
      break;
    case CHIP_ESP32C3:
      boardInfo = "ESP32-C3";
      break;
    default:
      boardInfo = "ESP32 (Unknown variant)";
      break;
  }

  return boardInfo;
}

void HardwareDiagnostics::printBoardInfo() {
  esp_chip_info_t chip_info;
  esp_chip_info(&chip_info);

  uint32_t flash_size;
  esp_flash_get_size(NULL, &flash_size);

  Serial.println("╔═══════════════════════════════════════════════════════════╗");
  Serial.println("║                    BOARD INFORMATION                     ║");
  Serial.println("╠═══════════════════════════════════════════════════════════╣");
  Serial.printf("║ Chip Model: %-45s ║\n", identifyBoard().c_str());
  Serial.printf("║ Chip Revision: %-42d ║\n", chip_info.revision);
  Serial.printf("║ CPU Cores: %-46d ║\n", chip_info.cores);
  Serial.printf("║ Flash Size: %-43d MB ║\n", flash_size / (1024 * 1024));
  Serial.printf("║ Free Heap: %-44d KB ║\n", ESP.getFreeHeap() / 1024);
  Serial.printf("║ WiFi: %-51s ║\n", (chip_info.features & CHIP_FEATURE_WIFI_BGN) ? "Yes" : "No");
  Serial.printf("║ Bluetooth: %-46s ║\n", (chip_info.features & CHIP_FEATURE_BT) ? "Yes" : "No");
  Serial.println("╚═══════════════════════════════════════════════════════════╝");
}

// ==================== PIN TESTING FUNCTIONS ====================
bool HardwareDiagnostics::isBootStrapPin(int pin) {
  int bootStrapPins[] = {0, 2, 4, 5, 12, 15};
  for (int i = 0; i < 6; i++) {
    if (pin == bootStrapPins[i]) return true;
  }
  return false;
}

bool HardwareDiagnostics::isReservedPin(int pin) {
  int reservedPins[] = {6, 7, 8, 9, 10, 11}; // Flash pins
  for (int i = 0; i < 6; i++) {
    if (pin == reservedPins[i]) return true;
  }
  return false;
}

bool HardwareDiagnostics::isPinSafe(int pin) {
  return !isBootStrapPin(pin) && !isReservedPin(pin) && pin >= 0 && pin <= 39;
}

String HardwareDiagnostics::getPinCapabilities(int pin) {
  String capabilities = "";

  // Check if it's an ADC pin
  if (pin >= 32 && pin <= 39) {
    capabilities += "ADC ";
  }
  if (pin >= 0 && pin <= 15) {
    capabilities += "ADC ";
  }

  // Check if it's input-only
  if (pin >= 34 && pin <= 39) {
    capabilities += "INPUT_ONLY ";
  } else {
    capabilities += "I/O ";
  }

  // Check for special functions
  if (pin == 21 || pin == 22) capabilities += "I2C ";
  if (pin == 18 || pin == 19 || pin == 23 || pin == 5) capabilities += "SPI ";
  if (pin == 1 || pin == 3) capabilities += "UART0 ";
  if (pin == 16 || pin == 17) capabilities += "UART2 ";

  return capabilities.length() > 0 ? capabilities : "DIGITAL";
}

// ==================== COMPONENT TESTING FUNCTIONS ====================
PinTestResult HardwareDiagnostics::testPin(int pin, String name) {
  PinTestResult result;
  result.pin = pin;
  result.name = name;
  result.isBootStrapPin = isBootStrapPin(pin);
  result.isReservedPin = isReservedPin(pin);
  result.warnings = "";

  // Test if pin can be read
  pinMode(pin, INPUT);
  delay(10);
  int readValue = digitalRead(pin);
  result.canRead = (readValue == 0 || readValue == 1);

  // Test if pin can be written (skip for input-only pins)
  if (pin >= 34 && pin <= 39) {
    result.canWrite = false;
    result.warnings += "INPUT_ONLY pin; ";
  } else {
    pinMode(pin, OUTPUT);
    digitalWrite(pin, HIGH);
    delay(10);
    pinMode(pin, INPUT);
    int highValue = digitalRead(pin);

    pinMode(pin, OUTPUT);
    digitalWrite(pin, LOW);
    delay(10);
    pinMode(pin, INPUT);
    int lowValue = digitalRead(pin);

    result.canWrite = (highValue != lowValue);
  }

  // Test internal pull-up
  pinMode(pin, INPUT_PULLUP);
  delay(10);
  int pullupValue = digitalRead(pin);
  result.hasInternalPullup = (pullupValue == HIGH);

  // Add warnings
  if (result.isBootStrapPin) {
    result.warnings += "BOOT_STRAP pin - affects boot mode; ";
  }
  if (result.isReservedPin) {
    result.warnings += "RESERVED for flash - DO NOT USE; ";
  }

  return result;
}

ComponentTestResult HardwareDiagnostics::testMoistureSensor(int pin) {
  ComponentTestResult result;
  result.componentName = "Soil Moisture Sensor";
  result.isConnected = false;
  result.isWorking = false;
  result.testValue = 0;
  result.errorMessage = "";
  result.recommendations = "";

  // Check if pin is suitable for analog reading
  bool isADCPin = (pin >= 32 && pin <= 39) || (pin >= 0 && pin <= 15);
  if (!isADCPin) {
    result.errorMessage = "Pin " + String(pin) + " is not an ADC pin";
    result.recommendations = "Use GPIO32, 33, 34, 35, 36, or 39 for analog sensors";
    return result;
  }

  pinMode(pin, INPUT);
  delay(100);

  // Take multiple readings to check for consistency
  int readings[5];
  bool hasVariation = false;

  for (int i = 0; i < 5; i++) {
    readings[i] = analogRead(pin);
    delay(50);
  }

  // Calculate average and check for variation
  long sum = 0;
  for (int i = 0; i < 5; i++) {
    sum += readings[i];
    if (i > 0 && abs(readings[i] - readings[i-1]) > 10) {
      hasVariation = true;
    }
  }

  result.testValue = sum / 5.0;

  // Determine if sensor is connected and working
  if (result.testValue < 10 || result.testValue > 4090) {
    result.errorMessage = "Sensor reading out of normal range";
    result.recommendations = "Check wiring: VCC to 3.3V, GND to GND, Signal to GPIO" + String(pin);
  } else if (!hasVariation) {
    result.errorMessage = "No variation in readings - sensor may be stuck";
    result.recommendations = "Try touching sensor or placing in different moisture levels";
  } else {
    result.isConnected = true;
    result.isWorking = true;
    result.recommendations = "Sensor appears to be working. Calibrate for accurate readings.";
  }

  return result;
}

ComponentTestResult HardwareDiagnostics::testDHTSensor(int pin) {
  ComponentTestResult result;
  result.componentName = "DHT22 Temperature/Humidity Sensor";
  result.isConnected = false;
  result.isWorking = false;
  result.testValue = 0;
  result.errorMessage = "";
  result.recommendations = "";

  // Clean up any existing DHT instance
  if (dht != nullptr) {
    delete dht;
  }

  // Create new DHT instance
  dht = new DHT(pin, DHT22);
  pinMode(pin, INPUT_PULLUP);
  delay(100);

  dht->begin();
  delay(2000); // DHT22 needs time to initialize

  // Test multiple readings
  bool anyValidReading = false;
  float totalTemp = 0;
  int validReadings = 0;

  for (int attempt = 0; attempt < 3; attempt++) {
    float temp = dht->readTemperature();
    float humidity = dht->readHumidity();

    if (!isnan(temp) && !isnan(humidity)) {
      anyValidReading = true;
      totalTemp += temp;
      validReadings++;
      result.testValue = temp;
    }

    delay(2500); // DHT22 needs 2 seconds between readings
  }

  if (anyValidReading) {
    result.isConnected = true;
    result.isWorking = true;
    result.testValue = totalTemp / validReadings;
    result.recommendations = "DHT22 is working properly";
  } else {
    result.errorMessage = "No valid readings from DHT22";
    result.recommendations = "Check wiring: VCC to 3.3V, GND to GND, Data to GPIO" + String(pin) + " with 4.7kΩ pull-up resistor";
  }

  return result;
}

ComponentTestResult HardwareDiagnostics::testRelayModule(int pin) {
  ComponentTestResult result;
  result.componentName = "Relay Module";
  result.isConnected = false;
  result.isWorking = false;
  result.testValue = 0;
  result.errorMessage = "";
  result.recommendations = "";

  // Check if pin can drive outputs
  if (pin >= 34 && pin <= 39) {
    result.errorMessage = "Pin " + String(pin) + " is input-only, cannot drive relay";
    result.recommendations = "Use GPIO18, 19, 21, 22, 23, 25, 26, or 27 for relay control";
    return result;
  }

  pinMode(pin, OUTPUT);

  // Test relay switching
  Serial.println("    Testing relay switching...");

  // Turn relay ON
  digitalWrite(pin, HIGH);
  delay(500);
  Serial.println("    Relay should be ON now - check for LED/click sound");

  // Turn relay OFF
  digitalWrite(pin, LOW);
  delay(500);
  Serial.println("    Relay should be OFF now");

  // Since we can't automatically detect relay operation, assume it's working
  // if the pin can be controlled
  result.isConnected = true;
  result.isWorking = true;
  result.testValue = 1; // Indicates successful test
  result.recommendations = "Relay test completed. Verify manually that relay switches ON/OFF with LED or sound.";

  return result;
}

// ==================== MAIN DIAGNOSTIC FUNCTIONS ====================
SystemDiagnostics HardwareDiagnostics::runFullDiagnostics() {
  SystemDiagnostics diagnostics;

  Serial.println("🔍 Running Full Hardware Diagnostics...");
  Serial.println();

  // Board information
  diagnostics.boardType = identifyBoard();
  esp_chip_info_t chip_info;
  esp_chip_info(&chip_info);
  diagnostics.chipRevision = chip_info.revision;

  uint32_t flash_size;
  esp_flash_get_size(NULL, &flash_size);
  diagnostics.flashSize = flash_size / (1024 * 1024);
  diagnostics.freeHeap = ESP.getFreeHeap();
  diagnostics.powerStable = (diagnostics.freeHeap > 100000); // Basic power check

  printBoardInfo();
  Serial.println();

  // Test current pin assignments
  Serial.println("🔧 Testing Current Pin Assignments:");
  diagnostics.moisturePin = testPin(moisturePin, "Moisture Sensor");
  diagnostics.dhtPin = testPin(dhtPin, "DHT22 Sensor");
  diagnostics.relayPin = testPin(relayPin, "Relay Control");

  // Print pin test results
  printSeparator("PIN ASSIGNMENT ANALYSIS");
  Serial.printf("Moisture Pin (GPIO%d): %s\n", moisturePin,
                diagnostics.moisturePin.warnings.length() > 0 ?
                ("⚠ " + diagnostics.moisturePin.warnings).c_str() : "✓ OK");
  Serial.printf("DHT22 Pin (GPIO%d): %s\n", dhtPin,
                diagnostics.dhtPin.warnings.length() > 0 ?
                ("⚠ " + diagnostics.dhtPin.warnings).c_str() : "✓ OK");
  Serial.printf("Relay Pin (GPIO%d): %s\n", relayPin,
                diagnostics.relayPin.warnings.length() > 0 ?
                ("⚠ " + diagnostics.relayPin.warnings).c_str() : "✓ OK");
  Serial.println();

  // Test individual components
  Serial.println("🧪 Testing Individual Components:");
  diagnostics.moistureSensor = testMoistureSensor(moisturePin);
  printComponentResult(diagnostics.moistureSensor);

  diagnostics.dhtSensor = testDHTSensor(dhtPin);
  printComponentResult(diagnostics.dhtSensor);

  diagnostics.relayModule = testRelayModule(relayPin);
  printComponentResult(diagnostics.relayModule);

  return diagnostics;
}

void HardwareDiagnostics::printComponentResult(ComponentTestResult result) {
  Serial.printf("  %s: ", result.componentName.c_str());

  if (result.isWorking) {
    Serial.printf("✓ WORKING (Value: %.1f)\n", result.testValue);
  } else if (result.isConnected) {
    Serial.printf("⚠ CONNECTED but not working properly\n");
  } else {
    Serial.printf("❌ NOT DETECTED\n");
  }

  if (result.errorMessage.length() > 0) {
    Serial.printf("    Error: %s\n", result.errorMessage.c_str());
  }

  if (result.recommendations.length() > 0) {
    Serial.printf("    Recommendation: %s\n", result.recommendations.c_str());
  }

  Serial.println();
}

void HardwareDiagnostics::printSeparator(String title) {
  if (title.length() > 0) {
    Serial.println("╔═══════════════════════════════════════════════════════════╗");
    Serial.printf("║ %-57s ║\n", title.c_str());
    Serial.println("╠═══════════════════════════════════════════════════════════╣");
  } else {
    Serial.println("═══════════════════════════════════════════════════════════");
  }
}

// ==================== PIN RECOMMENDATION FUNCTIONS ====================
void HardwareDiagnostics::validateCurrentPinAssignments() {
  Serial.println("🔍 Validating Current Pin Assignments...");
  Serial.println();

  // Check moisture sensor pin
  Serial.printf("Moisture Sensor (GPIO%d): ", moisturePin);
  if (isBootStrapPin(moisturePin)) {
    Serial.println("❌ BOOT STRAP PIN - Can cause boot issues!");
  } else if (isReservedPin(moisturePin)) {
    Serial.println("❌ RESERVED PIN - Do not use!");
  } else if (!(moisturePin >= 32 && moisturePin <= 39) && !(moisturePin >= 0 && moisturePin <= 15)) {
    Serial.println("⚠ Not an ADC pin - Cannot read analog values!");
  } else {
    Serial.println("✓ Pin assignment OK");
  }

  // Check DHT22 pin
  Serial.printf("DHT22 Sensor (GPIO%d): ", dhtPin);
  if (isBootStrapPin(dhtPin)) {
    Serial.println("❌ BOOT STRAP PIN - Can cause boot issues!");
  } else if (isReservedPin(dhtPin)) {
    Serial.println("❌ RESERVED PIN - Do not use!");
  } else {
    Serial.println("✓ Pin assignment OK");
  }

  // Check relay pin
  Serial.printf("Relay Control (GPIO%d): ", relayPin);
  if (isBootStrapPin(relayPin)) {
    Serial.println("❌ BOOT STRAP PIN - Can cause boot issues!");
  } else if (isReservedPin(relayPin)) {
    Serial.println("❌ RESERVED PIN - Do not use!");
  } else if (relayPin >= 34 && relayPin <= 39) {
    Serial.println("❌ INPUT-ONLY PIN - Cannot drive relay!");
  } else {
    Serial.println("✓ Pin assignment OK");
  }

  Serial.println();
}

void HardwareDiagnostics::recommendSafePins() {
  Serial.println("💡 RECOMMENDED SAFE PIN ASSIGNMENTS:");
  Serial.println("╔═══════════════════════════════════════════════════════════╗");
  Serial.println("║                    SAFE PIN RECOMMENDATIONS              ║");
  Serial.println("╠═══════════════════════════════════════════════════════════╣");
  Serial.println("║                                                           ║");
  Serial.println("║ SOIL MOISTURE SENSOR (needs ADC):                        ║");
  Serial.println("║   Recommended: GPIO32 (ADC4) - Safe analog pin           ║");
  Serial.println("║   Alternatives: GPIO33, GPIO34, GPIO35, GPIO36, GPIO39   ║");
  Serial.println("║                                                           ║");
  Serial.println("║ DHT22 TEMPERATURE SENSOR (digital I/O):                  ║");
  Serial.println("║   Recommended: GPIO21 (I2C SDA) - Safe digital pin       ║");
  Serial.println("║   Alternatives: GPIO22, GPIO16, GPIO17, GPIO25, GPIO26   ║");
  Serial.println("║                                                           ║");
  Serial.println("║ RELAY CONTROL (digital output):                          ║");
  Serial.println("║   Recommended: GPIO18 (VSPI SCK) - Safe output pin       ║");
  Serial.println("║   Alternatives: GPIO19, GPIO23, GPIO25, GPIO26, GPIO27   ║");
  Serial.println("║                                                           ║");
  Serial.println("║ PINS TO AVOID:                                           ║");
  Serial.println("║   Boot Strap: GPIO0, GPIO2, GPIO4, GPIO5, GPIO12, GPIO15 ║");
  Serial.println("║   Reserved: GPIO6-GPIO11 (Flash pins)                    ║");
  Serial.println("║   Input-Only: GPIO34-GPIO39 (cannot drive outputs)       ║");
  Serial.println("║                                                           ║");
  Serial.println("╚═══════════════════════════════════════════════════════════╝");
  Serial.println();

  Serial.println("🔧 SUGGESTED NEW PIN CONFIGURATION:");
  Serial.println("#define MOISTURE_PIN 32    // Safe ADC pin");
  Serial.println("#define DHT_PIN 21         // Safe digital I/O pin");
  Serial.println("#define RELAY_PIN 18       // Safe digital output pin");
  Serial.println();
}

// ==================== INTERACTIVE TESTING ====================
void HardwareDiagnostics::runInteractiveTest() {
  Serial.println("🎮 Interactive Component Testing Mode");
  Serial.println("Commands: 'moisture', 'dht', 'relay', 'pins', 'board', 'exit'");
  Serial.println("Type a command and press Enter:");

  while (true) {
    if (Serial.available()) {
      String command = Serial.readStringUntil('\n');
      command.trim();
      command.toLowerCase();

      if (command == "exit") {
        Serial.println("Exiting interactive mode...");
        break;
      } else if (command == "moisture") {
        Serial.println("\n--- Testing Moisture Sensor ---");
        ComponentTestResult result = testMoistureSensor(moisturePin);
        printComponentResult(result);
      } else if (command == "dht") {
        Serial.println("\n--- Testing DHT22 Sensor ---");
        ComponentTestResult result = testDHTSensor(dhtPin);
        printComponentResult(result);
      } else if (command == "relay") {
        Serial.println("\n--- Testing Relay Module ---");
        ComponentTestResult result = testRelayModule(relayPin);
        printComponentResult(result);
      } else if (command == "pins") {
        validateCurrentPinAssignments();
        recommendSafePins();
      } else if (command == "board") {
        printBoardInfo();
      } else {
        Serial.println("Unknown command. Available: moisture, dht, relay, pins, board, exit");
      }

      Serial.println("Enter next command:");
    }
    delay(100);
  }
}

bool HardwareDiagnostics::testIndividualComponent(String component) {
  component.toLowerCase();

  if (component == "moisture") {
    ComponentTestResult result = testMoistureSensor(moisturePin);
    printComponentResult(result);
    return result.isWorking;
  } else if (component == "dht" || component == "dht22") {
    ComponentTestResult result = testDHTSensor(dhtPin);
    printComponentResult(result);
    return result.isWorking;
  } else if (component == "relay") {
    ComponentTestResult result = testRelayModule(relayPin);
    printComponentResult(result);
    return result.isWorking;
  }

  Serial.println("Unknown component: " + component);
  return false;
}

// ==================== PIN SCANNING FUNCTION ====================
void HardwareDiagnostics::scanAllPins() {
  Serial.println("🔍 Scanning All GPIO Pins...");
  Serial.println();
  Serial.println("┌──────┬─────────────────────────────────────────────────────┬──────────┐");
  Serial.println("│ GPIO │ Capabilities                                        │ Status   │");
  Serial.println("├──────┼─────────────────────────────────────────────────────┼──────────┤");

  for (int pin = 0; pin <= 39; pin++) {
    String capabilities = getPinCapabilities(pin);
    String status = "";

    if (isReservedPin(pin)) {
      status = "RESERVED";
    } else if (isBootStrapPin(pin)) {
      status = "BOOT_STRAP";
    } else if (pin >= 34 && pin <= 39) {
      status = "INPUT_ONLY";
    } else {
      status = "SAFE";
    }

    Serial.printf("│ %2d   │ %-51s │ %-8s │\n", pin, capabilities.c_str(), status.c_str());
  }

  Serial.println("└──────┴─────────────────────────────────────────────────────┴──────────┘");
  Serial.println();
  Serial.println("Legend:");
  Serial.println("  SAFE       - Safe to use for any compatible function");
  Serial.println("  BOOT_STRAP - Affects boot mode, use with caution");
  Serial.println("  RESERVED   - Connected to flash, DO NOT USE");
  Serial.println("  INPUT_ONLY - Cannot drive outputs, read-only");
  Serial.println();
}