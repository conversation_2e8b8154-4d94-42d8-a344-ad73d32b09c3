# ESP32 Soil Moisture Monitoring System

A comprehensive soil moisture monitoring system for ESP32 with automatic irrigation control, temperature/humidity sensing, and robust hardware diagnostics.

## 🚨 IMPORTANT: Hardware Issues Fixed

**If you're experiencing hardware failures after switching ESP32 boards, this has been completely refactored with safe pin assignments and comprehensive diagnostics.**

### Previous Issues (Now Fixed):
- ❌ GPIO0 conflicts causing boot failures
- ❌ Boot strap pin usage causing unreliable operation
- ❌ Inconsistent pin documentation
- ❌ No hardware validation or error checking

### New Safe Configuration:
- ✅ **Soil Moisture Sensor**: GPIO32 (Safe ADC pin)
- ✅ **DHT22 Temperature/Humidity**: GPIO21 (Safe digital I/O)
- ✅ **Relay Control**: GPIO18 (Safe digital output)
- ✅ Comprehensive hardware diagnostics
- ✅ Individual component testing
- ✅ Automatic error detection and recovery

## 🔧 Quick Start - Hardware Diagnostics

**BEFORE running the main program, run the hardware diagnostics to identify any issues:**

1. **Upload the diagnostic program first:**
   ```bash
   # Temporarily rename main.cpp and use diagnostic_main.cpp
   mv src/main.cpp src/main_monitoring.cpp
   mv src/diagnostic_main.cpp src/main.cpp
   pio run --target upload
   ```

2. **Open Serial Monitor (115200 baud) and review the diagnostic report**

3. **Follow the recommendations to fix any hardware issues**

4. **Once all components are working, switch back to the monitoring program:**
   ```bash
   mv src/main.cpp src/diagnostic_main.cpp
   mv src/main_monitoring.cpp src/main.cpp
   pio run --target upload
   ```

## 📋 Hardware Requirements

### Components:
- **ESP32 Development Board** (NodeMCU-32S recommended)
- **Soil Moisture Sensor** (Capacitive type recommended)
- **DHT22** Temperature/Humidity Sensor
- **Relay Module** (5V with 3.3V logic input)
- **4.7kΩ Resistor** (pull-up for DHT22)
- **Jumper Wires** and **Breadboard**

### Wiring Connections:

| Component | ESP32 Pin | Notes |
|-----------|-----------|-------|
| **Soil Moisture Sensor** | | |
| VCC | 3.3V | Power supply |
| GND | GND | Ground |
| Signal | GPIO32 | Analog reading |
| **DHT22 Sensor** | | |
| VCC | 3.3V | Power supply |
| GND | GND | Ground |
| Data | GPIO21 | With 4.7kΩ pull-up to 3.3V |
| **Relay Module** | | |
| VCC | 5V (VIN) | Relay power |
| GND | GND | Ground |
| IN | GPIO18 | Control signal |

## 🚀 Features

### Core Functionality:
- **Real-time Monitoring**: Soil moisture, temperature, and humidity
- **Automatic Irrigation**: Relay-controlled watering based on moisture thresholds
- **Imperial/Metric Units**: Primary imperial with metric secondary display
- **Data Logging**: Formatted table output with timestamps
- **Error Handling**: Comprehensive sensor validation and recovery

### Advanced Features:
- **Hardware Diagnostics**: Complete system validation and troubleshooting
- **Interactive Testing**: Individual component testing and calibration
- **Safe Pin Management**: Automatic detection of problematic pin assignments
- **Board Identification**: Automatic ESP32 variant detection
- **Modular Architecture**: Easy to extend and modify

## 📊 Monitoring Display

The system provides a real-time table display:

```
┌──────┬─────────────────┬─────────────┬─────────────────┬────────┐
│ #    │ Temperature     │ Humidity    │ Soil Moisture   │ Relay  │
│      │ °F (°C)         │ %           │ % (Raw)         │ Status │
├──────┼─────────────────┼─────────────┼─────────────────┼────────┤
│    1 │  75.2°F (24.0°C)│     65.3%   │  45.2% (1250)   │ OFF    │
│    2 │  75.4°F (24.1°C)│     65.1%   │  44.8% (1260)   │ OFF    │
│    3 │  75.1°F (23.9°C)│     65.5%   │  35.2% (1420)   │ ON     │
```

## 🔍 Troubleshooting

### 1. Hardware Diagnostics
Run the diagnostic program to identify issues:
- Board type and capabilities
- Pin assignment validation
- Individual component testing
- Wiring verification

### 2. Common Issues and Solutions

#### Relay Not Working:
- ✅ Check wiring: VCC to 5V, GND to GND, IN to GPIO18
- ✅ Verify relay module LED lights up when activated
- ✅ Test with multimeter across relay contacts
- ✅ Ensure relay module is compatible with 3.3V logic

#### Soil Moisture Sensor Issues:
- ✅ Verify sensor is capacitive type (more reliable)
- ✅ Check wiring: VCC to 3.3V, GND to GND, Signal to GPIO32
- ✅ Run calibration helper to determine dry/wet values
- ✅ Ensure sensor probes are clean and making good soil contact

#### DHT22 Not Responding:
- ✅ Check wiring: VCC to 3.3V, GND to GND, Data to GPIO21
- ✅ **CRITICAL**: Install 4.7kΩ pull-up resistor between Data and VCC
- ✅ Verify DHT22 is genuine (many clones have issues)
- ✅ Try different DHT22 sensor if available

#### Boot Issues:
- ✅ Ensure you're using the NEW safe pin assignments
- ✅ Avoid connecting anything to GPIO0, GPIO2, GPIO15 during boot
- ✅ Check power supply stability (use quality USB cable/power adapter)

### 3. Calibration

#### Soil Moisture Calibration:
1. Use the calibration helper program (`calibration_helper.ino`)
2. Place sensor in completely dry soil, note the value
3. Place sensor in completely wet soil, note the value
4. Update `MOISTURE_DRY` and `MOISTURE_WET` constants
5. Adjust `MOISTURE_THRESHOLD` for irrigation trigger point

## 🛠️ Development

### Building and Uploading:
```bash
# Build the project
pio run

# Upload to ESP32
pio run --target upload

# Monitor serial output
pio device monitor
```

### Project Structure:
```
├── src/
│   ├── main.cpp                 # Main monitoring program
│   ├── diagnostic_main.cpp      # Hardware diagnostic program
│   ├── hardware_diagnostics.h   # Diagnostic module header
│   └── hardware_diagnostics.cpp # Diagnostic module implementation
├── examples/
│   └── calibration_helper.cpp   # Sensor calibration utility
├── pinout.md                    # Pin assignment documentation
├── pinoutnodemcu.md            # NodeMCU-32S pinout reference
└── platformio.ini              # PlatformIO configuration
```

### Adding New Sensors:
1. Add sensor configuration to `hardware_diagnostics.h`
2. Implement testing functions in `hardware_diagnostics.cpp`
3. Update main monitoring loop in `main.cpp`
4. Add calibration procedures if needed

## 📈 Configuration

### Key Constants (in `main.cpp`):
```cpp
// Pin assignments (SAFE pins)
#define MOISTURE_PIN 32      // ADC pin for soil sensor
#define DHT_PIN 21           // Digital I/O for DHT22
#define RELAY_PIN 18         // Digital output for relay

// Sensor configuration
#define MOISTURE_DRY 1500    // Raw value in dry soil
#define MOISTURE_WET 1000    // Raw value in wet soil
#define MOISTURE_THRESHOLD 40.0  // Irrigation trigger (%)
#define READING_INTERVAL 2500    // Reading interval (ms)
```

## 🔒 Safety Features

- **Boot Strap Pin Avoidance**: No critical pins used that affect boot mode
- **Input Validation**: All sensor readings validated before use
- **Error Recovery**: Automatic retry and fallback mechanisms
- **Hardware Protection**: Safe pin assignments prevent damage
- **Diagnostic Mode**: Comprehensive testing before operation

## 📝 License

This project is open source. Feel free to modify and distribute.

## 🤝 Contributing

1. Run hardware diagnostics on your setup
2. Test with your specific sensor combinations
3. Submit issues with diagnostic reports
4. Contribute improvements and additional sensor support

---

**⚠️ Important**: Always run the hardware diagnostic program first when setting up on a new ESP32 board or after hardware changes. This will save you hours of troubleshooting!
